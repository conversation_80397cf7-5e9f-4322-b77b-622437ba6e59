# -*- coding: utf-8 -*-
import re
from typing import List, Dict, Any


# 预编译正则表达式以提高性能
HTML_TAG_PATTERN = re.compile(r'<[^>]+>')
FUNCTION_PATTERN = re.compile(r'((?:p|parent)\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\()')
INTEGER_PATTERN = re.compile(r'^-?\d+$')
FLOAT_PATTERN = re.compile(r'^-?\d+\.\d+$')
FUNCTION_CALL_PATTERN = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_.]*\s*\(')


def parse_js_code(code_snippet: str) -> List[Dict[str, Any]]:
    """
    高性能JavaScript代码解析器，提取函数名和参数

    Args:
        code_snippet: JavaScript代码片段

    Returns:
        List[Dict]: 包含函数信息的列表
    """
    # 快速移除HTML标签
    clean_code = HTML_TAG_PATTERN.sub('', code_snippet)

    results = []
    pos = 0
    code_len = len(clean_code)

    # 使用字符串查找而不是正则表达式来提高性能
    while pos < code_len:
        # 查找 'p.' 或 'parent.'
        p_pos = clean_code.find('p.', pos)
        parent_pos = clean_code.find('parent.', pos)

        # 选择最近的位置
        if p_pos == -1 and parent_pos == -1:
            break
        elif p_pos == -1:
            next_pos = parent_pos
            prefix_len = 7  # len('parent.')
        elif parent_pos == -1:
            next_pos = p_pos
            prefix_len = 2  # len('p.')
        else:
            if p_pos < parent_pos:
                next_pos = p_pos
                prefix_len = 2
            else:
                next_pos = parent_pos
                prefix_len = 7

        # 快速解析函数调用
        func_info = fast_parse_function_call(clean_code, next_pos, prefix_len)
        if func_info:
            results.append(func_info)
            pos = func_info['end_pos']
        else:
            pos = next_pos + prefix_len

    return results


def fast_parse_function_call(code: str, start_pos: int, prefix_len: int) -> Dict[str, Any]:
    """
    快速解析单个函数调用
    """
    # 提取函数名
    func_start = start_pos + prefix_len
    paren_pos = code.find('(', func_start)
    if paren_pos == -1:
        return None

    function_name = code[func_start:paren_pos].strip()
    if not function_name or not function_name.replace('_', '').replace('$', '').isalnum():
        return None

    # 快速找到匹配的右括号
    paren_count = 1
    pos = paren_pos + 1
    params_start = pos

    while pos < len(code) and paren_count > 0:
        char = code[pos]
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
        elif char in ['"', "'"]:
            # 跳过字符串内容
            quote_char = char
            pos += 1
            while pos < len(code) and code[pos] != quote_char:
                if code[pos] == '\\':
                    pos += 1  # 跳过转义字符
                pos += 1
        pos += 1

    if paren_count != 0:
        return None

    params_str = code[params_start:pos-1]
    raw_call = code[start_pos:pos]

    # 快速解析参数
    parameters = fast_parse_parameters(params_str)

    return {
        'function_name': function_name,
        'parameters': parameters,
        'raw_call': raw_call,
        'end_pos': pos
    }


def fast_parse_parameters(params_str: str) -> List[Dict[str, Any]]:
    """
    高性能参数解析
    """
    if not params_str.strip():
        return []

    parameters = []
    current_param = []
    paren_count = 0
    bracket_count = 0
    in_string = False
    string_char = None
    i = 0

    while i < len(params_str):
        char = params_str[i]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
                current_param.append(char)
            elif char == '(':
                paren_count += 1
                current_param.append(char)
            elif char == ')':
                paren_count -= 1
                current_param.append(char)
            elif char == '[':
                bracket_count += 1
                current_param.append(char)
            elif char == ']':
                bracket_count -= 1
                current_param.append(char)
            elif char == ',' and paren_count == 0 and bracket_count == 0:
                # 参数分隔符
                param_str = ''.join(current_param).strip()
                if param_str:
                    parameters.append(fast_analyze_parameter(param_str))
                current_param = []
            else:
                current_param.append(char)
        else:
            current_param.append(char)
            if char == string_char:
                # 检查是否是转义的引号
                escape_count = 0
                j = i - 1
                while j >= 0 and params_str[j] == '\\':
                    escape_count += 1
                    j -= 1
                if escape_count % 2 == 0:  # 偶数个反斜杠，引号未被转义
                    in_string = False
                    string_char = None

        i += 1

    # 处理最后一个参数
    if current_param:
        param_str = ''.join(current_param).strip()
        if param_str:
            parameters.append(fast_analyze_parameter(param_str))

    return parameters


def fast_analyze_parameter(param: str) -> Dict[str, Any]:
    """
    高性能参数类型分析
    """
    if not param:
        return {'type': 'empty', 'value': '', 'raw': param}

    first_char = param[0]
    last_char = param[-1]

    # 字符串类型 - 最常见，优先检查
    if (first_char == '"' and last_char == '"') or \
       (first_char == "'" and last_char == "'"):
        return {
            'type': 'string',
            'value': param[1:-1],
            'raw': param
        }

    # 数组类型
    if first_char == '[' and last_char == ']':
        return {
            'type': 'array',
            'value': param,
            'raw': param
        }

    # 数字类型 - 使用字符检查而不是正则
    if first_char.isdigit() or (first_char == '-' and len(param) > 1 and param[1].isdigit()):
        if '.' in param:
            try:
                return {
                    'type': 'float',
                    'value': float(param),
                    'raw': param
                }
            except ValueError:
                pass
        else:
            try:
                return {
                    'type': 'integer',
                    'value': int(param),
                    'raw': param
                }
            except ValueError:
                pass

    # 布尔和null类型 - 使用字典查找
    lower_param = param.lower()
    if lower_param == 'true':
        return {'type': 'boolean', 'value': True, 'raw': param}
    elif lower_param == 'false':
        return {'type': 'boolean', 'value': False, 'raw': param}
    elif lower_param in ['null', 'undefined']:
        return {'type': 'null', 'value': None, 'raw': param}

    # 函数调用 - 简单检查
    if '(' in param and param.find('(') < param.rfind(')'):
        return {
            'type': 'function_call',
            'value': param,
            'raw': param
        }

    # 默认为标识符
    return {
        'type': 'identifier',
        'value': param,
        'raw': param
    }


if __name__ == "__main__":
    import time

    # 性能测试代码
    test_code = """
    <script>p.cls();p.cps('zone');p.setRoom('小径03',true);p._roomDesc('小径03','',[]);p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'');p.addUser(163546,p._getUser(163546,'Memory的猫','<font color=green>ご若秖如初見ご</font> Memory的猫','false','0',false,false,false,' ',false));</script>
    """ * 10  # 重复10次以测试性能

    # 性能测试
    start_time = time.perf_counter()
    results = parse_js_code(test_code)
    end_time = time.perf_counter()

    print(f"解析耗时: {(end_time - start_time) * 1000:.2f} 毫秒")
    print(f"解析到 {len(results)} 个函数调用")

    # 保存所有结果到文件
    with open('js_parse.txt', 'w', encoding='utf-8') as f:
        f.write(f"JavaScript解析结果\n")
        f.write(f"解析耗时: {(end_time - start_time) * 1000:.2f} 毫秒\n")
        f.write(f"解析到 {len(results)} 个函数调用\n")
        f.write("=" * 50 + "\n\n")

        # 显示所有结果
        for i, result in enumerate(results, 1):
            f.write(f"函数 {i}:\n")
            f.write(f"  函数名: {result['function_name']}\n")
            f.write(f"  参数数量: {len(result['parameters'])}\n")

            # 显示所有参数
            for j, param in enumerate(result['parameters']):
                f.write(f"    参数{j+1}: {param['type']} = {repr(param['value'])}\n")

            f.write(f"  原始调用: {result['raw_call']}\n")
            f.write("-" * 30 + "\n\n")

    print(f"结果已保存到 js_parse.txt 文件")
