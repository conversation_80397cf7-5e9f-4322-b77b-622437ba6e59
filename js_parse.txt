JavaScript解析结果
解析耗时: 0.63 毫秒
解析到 60 个函数调用
==================================================

函数 1:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 2:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 3:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 4:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 5:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 6:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 7:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 8:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 9:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 10:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 11:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 12:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 13:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 14:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 15:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 16:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 17:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 18:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 19:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 20:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 21:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 22:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 23:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 24:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 25:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 26:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 27:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 28:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 29:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 30:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 31:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 32:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 33:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 34:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 35:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 36:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 37:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 38:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 39:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 40:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 41:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 42:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 43:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 44:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 45:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 46:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 47:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 48:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 49:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 50:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 51:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 52:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 53:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 54:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

函数 55:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

函数 56:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

函数 57:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

函数 58:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

函数 59:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

函数 60:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

