JavaScript解析结果
解析耗时: 1.47 毫秒
解析到 80 个函数调用
==================================================

主函数 1:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 2:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 3:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 4:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 5:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 6:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 7:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 8:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 9:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 10:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 11:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 12:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 13:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 14:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 15:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 16:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 17:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 18:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 19:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 20:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 21:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 22:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 23:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 24:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 25:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 26:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 27:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 28:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 29:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 30:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 31:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 32:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 33:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 34:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 35:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 36:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 37:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 38:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 39:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 40:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 41:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 42:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 43:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 44:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 45:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 46:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 47:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 48:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 49:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 50:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 51:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 52:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 53:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 54:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 55:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 56:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 57:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 58:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 59:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 60:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 61:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 62:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 63:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 64:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 65:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 66:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 67:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 68:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 69:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 70:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 71:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 72:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

主函数 73:
  函数名: cls
  参数数量: 0
  原始调用: p.cls()
------------------------------

主函数 74:
  函数名: cps
  参数数量: 1
    参数1: string = 'zone'
  原始调用: p.cps('zone')
------------------------------

主函数 75:
  函数名: setRoom
  参数数量: 2
    参数1: string = '小径03'
    参数2: boolean = True
  原始调用: p.setRoom('小径03',true)
------------------------------

主函数 76:
  函数名: _roomDesc
  参数数量: 3
    参数1: string = '小径03'
    参数2: string = ''
    参数3: array = '[]'
  原始调用: p._roomDesc('小径03','',[])
------------------------------

主函数 77:
  函数名: addNpcs
  参数数量: 1
    参数1: function_call = "p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''"
  原始调用: p.addNpcs(p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+'')
------------------------------

子函数 78:
  函数名: _getNpc
  参数数量: 7
    参数1: integer = 0
    参数2: string = '绿毛虫'
    参数3: integer = 4369722721921
    参数4: string = '绿毛虫'
    参数5: integer = -1
    参数6: null = None
    参数7: boolean = False
  原始调用: p._getNpc(0,'绿毛虫',4369722721921,'绿毛虫',-1,null,false)+p._getNpc(1,'泡泡',4369742972650,'泡泡',-1,null,false)+''
------------------------------

主函数 79:
  函数名: addUser
  参数数量: 2
    参数1: integer = 163546
    参数2: function_call = "p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)"
  原始调用: p.addUser(163546,p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false))
------------------------------

子函数 80:
  函数名: _getUser
  参数数量: 10
    参数1: integer = 163546
    参数2: string = 'Memory的猫'
    参数3: string = 'ご若秖如初見ご Memory的猫'
    参数4: string = 'false'
    参数5: string = '0'
    参数6: boolean = False
    参数7: boolean = False
    参数8: boolean = False
    参数9: string = ' '
    参数10: boolean = False
  原始调用: p._getUser(163546,'Memory的猫','ご若秖如初見ご Memory的猫','false','0',false,false,false,' ',false)
------------------------------

