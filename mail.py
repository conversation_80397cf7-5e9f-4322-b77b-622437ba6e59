import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import datetime
import time

def send_qq_email(receiver: str, content: str, attachment_path: str = None, subject='小叮当-极-提醒邮件', sender='1425661289', auth_code='eidkyaigsbgdbabg'):
    """
    使用QQ邮箱发送邮件（支持附件）

    :param sender: 发件人邮箱（QQ邮箱地址）
    :param auth_code: QQ邮箱SMTP授权码
    :param receiver: 收件人邮箱地址
    :param subject: 邮件主题
    :param content: 邮件正文
    :param attachment_path: 可选，附件文件路径
    """
    # 构建邮件对象
    msg = EmailMessage()
    msg["From"] = sender
    msg["To"] = receiver
    msg["Subject"] = subject
    msg.set_content(content)

    # 如果有附件
    if attachment_path and os.path.isfile(attachment_path):
        with open(attachment_path, "rb") as f:
            file_data = f.read()
            file_name = os.path.basename(attachment_path)
            msg.add_attachment(file_data, maintype="application", subtype="octet-stream", filename=file_name)

    # 发送邮件（使用 SSL）
    try:
        with smtplib.SMTP_SSL("smtp.qq.com", 465) as smtp:
            smtp.login(sender, auth_code)
            smtp.send_message(msg)
        print("✅ 邮件发送成功")
    except Exception as e:
        print("❌ 邮件发送失败:", e)

if __name__ == '__main__':
    send_qq_email('<EMAIL>', '测试邮件')