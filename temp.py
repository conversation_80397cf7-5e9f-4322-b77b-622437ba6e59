import re
s = "p._getNpc(0,'绿毛虫',4597826933377,'绿毛虫',-1,null,false)+p._getNpc(1,'黑蘑菇',11224229975299,'黑蘑菇',-1,null,false)+p._getNpc(2,'绿毛虫',4759792263919,'绿毛虫',-1,null,false)+p._getNpc(3,'草原蝎',4606352490286,'草原蝎',-1,null,false)+p._getNpc(4,'草原蝎',4597745930461,'草原蝎',-1,null,false)+p._getNpc(5,'机警小鸡',4655278251550,'机警小鸡',-1,null,false)+p._getNpc(6,'机警小鸡',1368382427560,'机警小鸡',-1,null,false)+p._getNpc(7,'草原蝎',6679379116558,'草原蝎',-1,null,false)+p._getNpc(8,'红蘑菇',14877967755124,'红蘑菇',-1,null,false)+p._getNpc(9,'红蘑菇',4759731511732,'红蘑菇',-1,null,false)+p._getNpc(10,'红蘑菇',15233408550532,'红蘑菇',-1,null,false)+''"

r = re.compile(r"p\._getNpc\(([^)]*)\)")
li = r.findall(s)
print([i.replace("'", '').replace(" ", '').split(',') for i in li])